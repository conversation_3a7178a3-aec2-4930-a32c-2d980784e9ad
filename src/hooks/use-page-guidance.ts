'use client';

import { useEffect, useMemo } from 'react';
import { useGuidance } from '@/contexts';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface UsePageGuidanceOptions {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	defaultOpen?: boolean;
}

export function usePageGuidance(options: UsePageGuidanceOptions) {
	const { showGuidance, hideGuidance, isOpen, config } = useGuidance();

	// Memoize the guidance config to prevent unnecessary re-renders
	const guidanceConfig = useMemo(
		() => ({
			titleKey: options.titleKey,
			steps: options.steps,
			tipKey: options.tipKey,
			requirementKey: options.requirementKey,
			defaultOpen: options.defaultOpen || false,
		}),
		[
			options.titleKey,
			JSON.stringify(options.steps), // Stringify to compare array contents
			options.tipKey,
			options.requirementKey,
			options.defaultOpen,
		]
	);

	useEffect(() => {
		// Register guidance config
		showGuidance(guidanceConfig);

		// Cleanup when component unmounts
		return () => {
			hideGuidance();
		};
	}, [guidanceConfig]); // Only depend on the memoized config

	return {
		isOpen,
		config,
		hideGuidance,
	};
}
